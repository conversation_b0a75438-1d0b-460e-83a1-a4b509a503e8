"""
Statistical Tests Module for Safe RL Framework

Provides comprehensive statistical analysis tools including p-values, effect sizes,
significance testing, and performance comparisons for Safe RL experiments.
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, List, Tuple, Optional, Union
from dataclasses import dataclass
from pathlib import Path
import json

import scipy.stats as stats
from scipy.stats import ttest_rel, ttest_ind, wilcoxon, mannwhitneyu

from rich.console import <PERSON>sole
from rich.table import Table

console = Console()

def _compute_statistical_power(effect_size: float, n: int, alpha: float = 0.05) -> float:
    """Compute statistical power for t-test (simplified approximation)"""
    try:
        # Simplified power calculation using normal approximation
        from scipy.stats import norm

        # Critical value for two-tailed test
        z_alpha = norm.ppf(1 - alpha / 2)

        # Non-centrality parameter
        delta = effect_size * np.sqrt(n / 2)  # For two-sample case

        # Power calculation
        power = 1 - norm.cdf(z_alpha - delta) + norm.cdf(-z_alpha - delta)

        return max(0.0, min(1.0, power))  # Clamp between 0 and 1
    except:
        return 0.5  # Default fallback

def _benjamini_hochberg_correction(p_values: List[float], alpha: float = 0.05) -> Tuple[List[bool], List[float]]:
    """Implement Benjamini-Hochberg FDR correction"""
    n = len(p_values)
    if n == 0:
        return [], []

    # Sort p-values with original indices
    sorted_indices = np.argsort(p_values)
    sorted_p_values = np.array(p_values)[sorted_indices]

    # Apply BH procedure
    rejected = np.zeros(n, dtype=bool)
    corrected_p = np.zeros(n)

    for i in range(n - 1, -1, -1):  # Start from largest p-value
        corrected_p[sorted_indices[i]] = min(1.0, sorted_p_values[i] * n / (i + 1))
        if sorted_p_values[i] <= alpha * (i + 1) / n:
            rejected[sorted_indices[i]] = True
            # All smaller p-values are also rejected
            for j in range(i):
                rejected[sorted_indices[j]] = True
            break

    return rejected.tolist(), corrected_p.tolist()

@dataclass
class StatisticalResult:
    """Container for statistical test results"""
    test_name: str
    statistic: float
    p_value: float
    effect_size: float
    effect_size_interpretation: str
    confidence_interval: Tuple[float, float]
    significant: bool
    power: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "test_name": self.test_name,
            "statistic": self.statistic,
            "p_value": self.p_value,
            "effect_size": self.effect_size,
            "effect_size_interpretation": self.effect_size_interpretation,
            "confidence_interval": list(self.confidence_interval),
            "significant": self.significant,
            "power": self.power,
        }

def compute_effect_size(group1: np.ndarray, group2: np.ndarray, 
                       method: str = "cohen_d") -> Tuple[float, str]:
    """Compute effect size between two groups"""
    
    if method == "cohen_d":
        # Cohen's d for independent samples
        pooled_std = np.sqrt(((len(group1) - 1) * np.var(group1, ddof=1) + 
                             (len(group2) - 1) * np.var(group2, ddof=1)) / 
                            (len(group1) + len(group2) - 2))
        
        if pooled_std == 0:
            effect_size = 0.0
        else:
            effect_size = (np.mean(group1) - np.mean(group2)) / pooled_std
    
    elif method == "glass_delta":
        # Glass's delta (uses control group std)
        control_std = np.std(group2, ddof=1)
        if control_std == 0:
            effect_size = 0.0
        else:
            effect_size = (np.mean(group1) - np.mean(group2)) / control_std
    
    elif method == "hedges_g":
        # Hedges' g (bias-corrected Cohen's d)
        cohen_d, _ = compute_effect_size(group1, group2, "cohen_d")
        n = len(group1) + len(group2)
        correction = 1 - (3 / (4 * n - 9))
        effect_size = cohen_d * correction
    
    else:
        raise ValueError(f"Unknown effect size method: {method}")
    
    # Interpret effect size
    abs_effect = abs(effect_size)
    if abs_effect < 0.2:
        interpretation = "negligible"
    elif abs_effect < 0.5:
        interpretation = "small"
    elif abs_effect < 0.8:
        interpretation = "medium"
    else:
        interpretation = "large"
    
    return effect_size, interpretation

def paired_t_test(before: np.ndarray, after: np.ndarray, 
                 alpha: float = 0.05) -> StatisticalResult:
    """Perform paired t-test"""
    
    # Perform test
    statistic, p_value = ttest_rel(before, after)
    
    # Effect size (Cohen's d for paired samples)
    diff = after - before
    effect_size = np.mean(diff) / np.std(diff, ddof=1)
    
    # Interpret effect size
    abs_effect = abs(effect_size)
    if abs_effect < 0.2:
        interpretation = "negligible"
    elif abs_effect < 0.5:
        interpretation = "small"
    elif abs_effect < 0.8:
        interpretation = "medium"
    else:
        interpretation = "large"
    
    # Confidence interval for mean difference
    n = len(diff)
    se = stats.sem(diff)
    ci = stats.t.interval(1 - alpha, n - 1, loc=np.mean(diff), scale=se)
    
    # Statistical power
    power = _compute_statistical_power(effect_size, n, alpha)
    
    return StatisticalResult(
        test_name="Paired t-test",
        statistic=statistic,
        p_value=p_value,
        effect_size=effect_size,
        effect_size_interpretation=interpretation,
        confidence_interval=ci,
        significant=p_value < alpha,
        power=power,
    )

def wilcoxon_test(before: np.ndarray, after: np.ndarray, 
                 alpha: float = 0.05) -> StatisticalResult:
    """Perform Wilcoxon signed-rank test"""
    
    # Perform test
    statistic, p_value = wilcoxon(before, after, alternative='two-sided')
    
    # Effect size (r = Z / sqrt(N))
    n = len(before)
    z_score = stats.norm.ppf(1 - p_value / 2)  # Approximate Z from p-value
    effect_size = z_score / np.sqrt(n)
    
    # Interpret effect size
    abs_effect = abs(effect_size)
    if abs_effect < 0.1:
        interpretation = "negligible"
    elif abs_effect < 0.3:
        interpretation = "small"
    elif abs_effect < 0.5:
        interpretation = "medium"
    else:
        interpretation = "large"
    
    # Bootstrap confidence interval for median difference
    diff = after - before
    ci = bootstrap_confidence_interval(diff, np.median, alpha=alpha)
    
    return StatisticalResult(
        test_name="Wilcoxon signed-rank test",
        statistic=statistic,
        p_value=p_value,
        effect_size=effect_size,
        effect_size_interpretation=interpretation,
        confidence_interval=ci,
        significant=p_value < alpha,
    )

def bootstrap_confidence_interval(data: np.ndarray, statistic_func: callable, 
                                n_bootstrap: int = 1000, alpha: float = 0.05) -> Tuple[float, float]:
    """Compute bootstrap confidence interval"""
    
    bootstrap_stats = []
    n = len(data)
    
    for _ in range(n_bootstrap):
        bootstrap_sample = np.random.choice(data, size=n, replace=True)
        bootstrap_stats.append(statistic_func(bootstrap_sample))
    
    bootstrap_stats = np.array(bootstrap_stats)
    lower_percentile = (alpha / 2) * 100
    upper_percentile = (1 - alpha / 2) * 100
    
    ci_lower = np.percentile(bootstrap_stats, lower_percentile)
    ci_upper = np.percentile(bootstrap_stats, upper_percentile)
    
    return (ci_lower, ci_upper)

class StatisticalAnalyzer:
    """Main statistical analysis class"""
    
    def __init__(self, alpha: float = 0.05):
        self.alpha = alpha
        self.results: Dict[str, StatisticalResult] = {}
    
    def compare_groups(self, group1: np.ndarray, group2: np.ndarray, 
                      group1_name: str = "Group 1", group2_name: str = "Group 2",
                      paired: bool = False, parametric: bool = True) -> StatisticalResult:
        """Compare two groups with appropriate statistical test"""
        
        if paired:
            if parametric:
                result = paired_t_test(group1, group2, self.alpha)
            else:
                result = wilcoxon_test(group1, group2, self.alpha)
        else:
            if parametric:
                # Independent t-test
                statistic, p_value = ttest_ind(group1, group2)
                effect_size, interpretation = compute_effect_size(group1, group2, "cohen_d")
                
                # Confidence interval for mean difference
                diff_mean = np.mean(group1) - np.mean(group2)
                se_diff = np.sqrt(np.var(group1, ddof=1)/len(group1) + np.var(group2, ddof=1)/len(group2))
                df = len(group1) + len(group2) - 2
                ci = stats.t.interval(1 - self.alpha, df, loc=diff_mean, scale=se_diff)
                
                # Statistical power
                n_harmonic = 2 / (1/len(group1) + 1/len(group2))  # Harmonic mean
                power = _compute_statistical_power(effect_size, n_harmonic, self.alpha)
                
                result = StatisticalResult(
                    test_name="Independent t-test",
                    statistic=statistic,
                    p_value=p_value,
                    effect_size=effect_size,
                    effect_size_interpretation=interpretation,
                    confidence_interval=ci,
                    significant=p_value < self.alpha,
                    power=power,
                )
            else:
                # Mann-Whitney U test
                statistic, p_value = mannwhitneyu(group1, group2, alternative='two-sided')
                
                # Effect size (r = Z / sqrt(N))
                n_total = len(group1) + len(group2)
                z_score = stats.norm.ppf(1 - p_value / 2)
                effect_size = z_score / np.sqrt(n_total)
                
                abs_effect = abs(effect_size)
                if abs_effect < 0.1:
                    interpretation = "negligible"
                elif abs_effect < 0.3:
                    interpretation = "small"
                elif abs_effect < 0.5:
                    interpretation = "medium"
                else:
                    interpretation = "large"
                
                # Bootstrap CI for median difference
                combined_data = np.concatenate([group1, group2])
                ci = bootstrap_confidence_interval(combined_data, np.median, alpha=self.alpha)
                
                result = StatisticalResult(
                    test_name="Mann-Whitney U test",
                    statistic=statistic,
                    p_value=p_value,
                    effect_size=effect_size,
                    effect_size_interpretation=interpretation,
                    confidence_interval=ci,
                    significant=p_value < self.alpha,
                )
        
        # Store result
        comparison_name = f"{group1_name}_vs_{group2_name}"
        self.results[comparison_name] = result
        
        return result
    
    def multiple_comparisons_correction(self, method: str = "bonferroni") -> Dict[str, StatisticalResult]:
        """Apply multiple comparisons correction"""
        
        if not self.results:
            return {}
        
        p_values = [result.p_value for result in self.results.values()]
        
        if method == "bonferroni":
            corrected_alpha = self.alpha / len(p_values)
            corrected_results = {}
            
            for name, result in self.results.items():
                corrected_result = StatisticalResult(
                    test_name=f"{result.test_name} (Bonferroni corrected)",
                    statistic=result.statistic,
                    p_value=result.p_value,
                    effect_size=result.effect_size,
                    effect_size_interpretation=result.effect_size_interpretation,
                    confidence_interval=result.confidence_interval,
                    significant=result.p_value < corrected_alpha,
                    power=result.power,
                )
                corrected_results[name] = corrected_result
            
            return corrected_results
        
        elif method == "fdr_bh":
            # Benjamini-Hochberg FDR correction
            rejected, p_corrected = _benjamini_hochberg_correction(p_values, self.alpha)
            
            corrected_results = {}
            for i, (name, result) in enumerate(self.results.items()):
                corrected_result = StatisticalResult(
                    test_name=f"{result.test_name} (FDR corrected)",
                    statistic=result.statistic,
                    p_value=p_corrected[i],
                    effect_size=result.effect_size,
                    effect_size_interpretation=result.effect_size_interpretation,
                    confidence_interval=result.confidence_interval,
                    significant=rejected[i],
                    power=result.power,
                )
                corrected_results[name] = corrected_result
            
            return corrected_results
        
        else:
            raise ValueError(f"Unknown correction method: {method}")
    
    def print_results(self, corrected: bool = False):
        """Print statistical results in a formatted table"""
        
        results_to_show = self.multiple_comparisons_correction() if corrected else self.results
        
        if not results_to_show:
            console.print("[yellow]No statistical results available[/yellow]")
            return
        
        table = Table(title="Statistical Analysis Results")
        table.add_column("Comparison", style="cyan")
        table.add_column("Test", style="blue")
        table.add_column("Statistic", style="green")
        table.add_column("p-value", style="yellow")
        table.add_column("Effect Size", style="magenta")
        table.add_column("Interpretation", style="red")
        table.add_column("Significant", style="bold")
        
        for name, result in results_to_show.items():
            significance = "✓" if result.significant else "✗"
            sig_color = "green" if result.significant else "red"
            
            table.add_row(
                name.replace("_", " "),
                result.test_name,
                f"{result.statistic:.3f}",
                f"{result.p_value:.4f}",
                f"{result.effect_size:.3f}",
                result.effect_size_interpretation,
                f"[{sig_color}]{significance}[/{sig_color}]",
            )
        
        console.print(table)
    
    def save_results(self, filepath: Union[str, Path], corrected: bool = False):
        """Save statistical results to file"""
        filepath = Path(filepath)
        
        results_to_save = self.multiple_comparisons_correction() if corrected else self.results
        
        data = {
            "alpha": self.alpha,
            "corrected": corrected,
            "results": {name: result.to_dict() for name, result in results_to_save.items()}
        }
        
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)
        
        console.print(f"[green]Statistical results saved to {filepath}[/green]")

class PerformanceComparator:
    """Compare performance across different agents/algorithms"""

    def __init__(self, alpha: float = 0.05):
        self.alpha = alpha
        self.analyzer = StatisticalAnalyzer(alpha)
        self.performance_data: Dict[str, Dict[str, np.ndarray]] = {}

    def add_performance_data(self, agent_name: str, metric_name: str, values: np.ndarray):
        """Add performance data for an agent"""
        if agent_name not in self.performance_data:
            self.performance_data[agent_name] = {}
        self.performance_data[agent_name][metric_name] = values

    def compare_agents(self, agent1: str, agent2: str, metric: str,
                      paired: bool = False, parametric: bool = True) -> StatisticalResult:
        """Compare two agents on a specific metric"""

        if agent1 not in self.performance_data or agent2 not in self.performance_data:
            raise ValueError(f"Performance data not available for {agent1} or {agent2}")

        if metric not in self.performance_data[agent1] or metric not in self.performance_data[agent2]:
            raise ValueError(f"Metric {metric} not available for both agents")

        data1 = self.performance_data[agent1][metric]
        data2 = self.performance_data[agent2][metric]

        return self.analyzer.compare_groups(
            data1, data2, agent1, agent2, paired=paired, parametric=parametric
        )

    def pairwise_comparisons(self, metric: str, parametric: bool = True) -> Dict[str, StatisticalResult]:
        """Perform pairwise comparisons between all agents for a metric"""

        agents = list(self.performance_data.keys())
        results = {}

        for i in range(len(agents)):
            for j in range(i + 1, len(agents)):
                agent1, agent2 = agents[i], agents[j]

                if (metric in self.performance_data[agent1] and
                    metric in self.performance_data[agent2]):

                    result = self.compare_agents(agent1, agent2, metric,
                                               paired=False, parametric=parametric)
                    results[f"{agent1}_vs_{agent2}"] = result

        return results

    def rank_agents(self, metric: str, higher_is_better: bool = True) -> List[Tuple[str, float, float]]:
        """Rank agents by performance on a metric"""

        rankings = []
        for agent_name, metrics in self.performance_data.items():
            if metric in metrics:
                values = metrics[metric]
                mean_performance = np.mean(values)
                std_performance = np.std(values, ddof=1)
                rankings.append((agent_name, mean_performance, std_performance))

        rankings.sort(key=lambda x: x[1], reverse=higher_is_better)
        return rankings

class SafetyStatistics:
    """Statistical analysis specific to safety metrics"""

    def __init__(self, alpha: float = 0.05):
        self.alpha = alpha
        self.analyzer = StatisticalAnalyzer(alpha)

    def analyze_violation_rates(self, agent_violations: Dict[str, np.ndarray]) -> Dict[str, Any]:
        """Analyze safety violation rates across agents"""

        results = {
            "violation_rate_stats": {},
            "pairwise_comparisons": {},
            "rankings": [],
        }

        # Calculate violation rate statistics
        for agent_name, violations in agent_violations.items():
            violation_rate = np.mean(violations)
            violation_std = np.std(violations, ddof=1)

            # Confidence interval for violation rate
            n = len(violations)
            se = violation_std / np.sqrt(n)
            ci = stats.t.interval(1 - self.alpha, n - 1, loc=violation_rate, scale=se)

            results["violation_rate_stats"][agent_name] = {
                "mean_violation_rate": violation_rate,
                "std_violation_rate": violation_std,
                "confidence_interval": ci,
                "total_violations": np.sum(violations),
                "episodes_with_violations": np.sum(violations > 0),
                "max_violations_per_episode": np.max(violations),
            }

        # Pairwise comparisons
        agents = list(agent_violations.keys())
        for i in range(len(agents)):
            for j in range(i + 1, len(agents)):
                agent1, agent2 = agents[i], agents[j]

                result = self.analyzer.compare_groups(
                    agent_violations[agent1],
                    agent_violations[agent2],
                    agent1, agent2,
                    paired=False,
                    parametric=False  # Use non-parametric for count data
                )
                results["pairwise_comparisons"][f"{agent1}_vs_{agent2}"] = result.to_dict()

        # Rank agents by safety (lower violation rate is better)
        rankings = [(agent, stats["mean_violation_rate"], stats["std_violation_rate"])
                   for agent, stats in results["violation_rate_stats"].items()]
        rankings.sort(key=lambda x: x[1])  # Lower is better for violations
        results["rankings"] = rankings

        return results

    def safety_budget_analysis(self, agent_costs: Dict[str, np.ndarray],
                             budget_threshold: float = 0.1) -> Dict[str, Any]:
        """Analyze safety budget compliance"""

        results = {
            "budget_compliance": {},
            "budget_utilization": {},
        }

        for agent_name, costs in agent_costs.items():
            # Calculate cumulative costs per episode
            cumulative_costs = np.cumsum(costs)
            episodes = np.arange(1, len(costs) + 1)

            # Budget compliance rate
            budget_violations = cumulative_costs / episodes > budget_threshold
            compliance_rate = 1 - np.mean(budget_violations)

            # Budget utilization
            final_utilization = cumulative_costs[-1] / len(costs) if len(costs) > 0 else 0
            utilization_efficiency = min(final_utilization / budget_threshold, 1.0)

            results["budget_compliance"][agent_name] = {
                "compliance_rate": compliance_rate,
                "budget_violations": np.sum(budget_violations),
                "final_budget_utilization": final_utilization,
                "utilization_efficiency": utilization_efficiency,
                "budget_threshold": budget_threshold,
            }

        return results

    def constraint_satisfaction_analysis(self, agent_constraints: Dict[str, np.ndarray]) -> Dict[str, Any]:
        """Analyze constraint satisfaction across agents"""

        results = {
            "satisfaction_rates": {},
            "statistical_comparisons": {},
        }

        # Calculate satisfaction rates
        for agent_name, constraints in agent_constraints.items():
            # Assuming constraints are binary (0 = satisfied, 1 = violated)
            satisfaction_rate = 1 - np.mean(constraints)
            satisfaction_std = np.std(1 - constraints, ddof=1)

            # Confidence interval
            n = len(constraints)
            se = satisfaction_std / np.sqrt(n)
            ci = stats.t.interval(1 - self.alpha, n - 1, loc=satisfaction_rate, scale=se)

            results["satisfaction_rates"][agent_name] = {
                "satisfaction_rate": satisfaction_rate,
                "satisfaction_std": satisfaction_std,
                "confidence_interval": ci,
                "total_violations": np.sum(constraints),
                "violation_episodes": np.sum(constraints > 0),
            }

        # Statistical comparisons
        agents = list(agent_constraints.keys())
        for i in range(len(agents)):
            for j in range(i + 1, len(agents)):
                agent1, agent2 = agents[i], agents[j]

                # Compare satisfaction rates (invert constraints for comparison)
                satisfaction1 = 1 - agent_constraints[agent1]
                satisfaction2 = 1 - agent_constraints[agent2]

                result = self.analyzer.compare_groups(
                    satisfaction1, satisfaction2, agent1, agent2,
                    paired=False, parametric=True
                )
                results["statistical_comparisons"][f"{agent1}_vs_{agent2}"] = result.to_dict()

        return results
