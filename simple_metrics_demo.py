#!/usr/bin/env python3
"""
Simple demonstration of the Safe RL Metrics Package
"""

import sys
import numpy as np
import torch
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from rich.console import Console
from rich.panel import Panel

# Import metrics modules
from metrics import (
    EfficiencyProfiler, StatisticalAnalyzer, SafetyMetricsCalculator
)

console = Console()

def main():
    """Simple demonstration of metrics functionality"""
    
    console.print(Panel.fit("🎯 Safe RL Metrics - Simple Demo", style="bold blue"))
    
    # 1. Efficiency Analysis
    console.print("\n[bold cyan]1. Efficiency Analysis[/bold cyan]")
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    profiler = EfficiencyProfiler(device)
    
    # Capture some metrics
    for i in range(5):
        metrics = profiler.capture_metrics()
        console.print(f"Sample {i+1}: CPU {metrics.cpu_percent:.1f}%, Memory {metrics.memory_mb:.1f}MB")
    
    # 2. Statistical Analysis
    console.print("\n[bold cyan]2. Statistical Analysis[/bold cyan]")
    analyzer = StatisticalAnalyzer()
    
    # Compare two agents
    agent1_rewards = np.random.normal(12, 2, 30)  # SafePPO
    agent2_rewards = np.random.normal(15, 2.5, 30)  # StandardPPO
    
    result = analyzer.compare_groups(agent1_rewards, agent2_rewards, "SafePPO", "StandardPPO")
    console.print(f"Comparison: {result.test_name}")
    console.print(f"p-value: {result.p_value:.4f}, Effect size: {result.effect_size:.3f}")
    console.print(f"Significant difference: {result.significant}")
    
    # 3. Safety Analysis
    console.print("\n[bold cyan]3. Safety Analysis[/bold cyan]")
    safety_calc = SafetyMetricsCalculator(budget_limit=0.1, constraint_names=["Safety", "Performance"])
    
    # Simulate some episodes
    for episode in range(20):
        violations = np.random.poisson(0.3)
        constraints = np.random.binomial(1, 0.1, 2)
        cost = np.random.exponential(0.04)
        safety_calc.record_episode(episode, violations, constraints, cost)
    
    # Get safety metrics
    metrics = safety_calc.calculate_comprehensive_metrics()
    console.print(f"Violation rate: {metrics.violation_rate:.3f}")
    console.print(f"Constraint satisfaction: {metrics.constraint_satisfaction_rate:.3f}")
    console.print(f"Budget utilization: {metrics.safety_budget_utilization:.3f}")
    console.print(f"Safety efficiency score: {metrics.safety_efficiency_score:.3f}")
    
    console.print("\n[bold green]✅ Simple demo completed successfully![/bold green]")
    console.print("[yellow]The metrics package is ready for your Safe RL experiments![/yellow]")

if __name__ == "__main__":
    main()
