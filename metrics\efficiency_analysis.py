"""
Efficiency Analysis Module for Safe RL Framework

Provides comprehensive GPU/CPU/memory profiling and performance analysis tools
for PyTorch-based Safe RL training with Windows native support.
"""

import time
import psutil
import threading
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass
from contextlib import contextmanager
from pathlib import Path
import json

import torch
import numpy as np
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn

console = Console()

@dataclass
class ProfileMetrics:
    """Container for profiling metrics"""
    timestamp: float
    cpu_percent: float
    memory_mb: float
    gpu_memory_mb: float = 0.0
    gpu_utilization: float = 0.0
    gpu_temperature: float = 0.0
    execution_time: float = 0.0
    throughput: float = 0.0
    
    def to_dict(self) -> Dict[str, float]:
        """Convert to dictionary"""
        return {
            "timestamp": self.timestamp,
            "cpu_percent": self.cpu_percent,
            "memory_mb": self.memory_mb,
            "gpu_memory_mb": self.gpu_memory_mb,
            "gpu_utilization": self.gpu_utilization,
            "gpu_temperature": self.gpu_temperature,
            "execution_time": self.execution_time,
            "throughput": self.throughput,
        }

class GPUProfiler:
    """GPU profiling utilities with Windows support"""
    
    def __init__(self):
        self.cuda_available = torch.cuda.is_available()
        self.device_count = torch.cuda.device_count() if self.cuda_available else 0
        self.nvidia_ml_available = self._check_nvidia_ml()
        
    def _check_nvidia_ml(self) -> bool:
        """Check if nvidia-ml-py3 is available"""
        try:
            import nvidia_ml_py3 as nvml
            nvml.nvmlInit()
            return True
        except (ImportError, Exception):
            # nvidia-ml-py3 not available, which is fine
            return False
    
    def get_gpu_info(self, device_id: int = 0) -> Dict[str, Any]:
        """Get comprehensive GPU information"""
        if not self.cuda_available:
            return {"available": False}
        
        info = {
            "available": True,
            "device_id": device_id,
            "name": torch.cuda.get_device_name(device_id),
            "memory_total": torch.cuda.get_device_properties(device_id).total_memory / (1024**3),
            "memory_allocated": torch.cuda.memory_allocated(device_id) / (1024**3),
            "memory_reserved": torch.cuda.memory_reserved(device_id) / (1024**3),
            "utilization": 0.0,
            "temperature": 0.0,
        }
        
        # Get detailed metrics if nvidia-ml is available
        if self.nvidia_ml_available:
            try:
                import nvidia_ml_py3 as nvml
                handle = nvml.nvmlDeviceGetHandleByIndex(device_id)
                
                # GPU utilization
                util = nvml.nvmlDeviceGetUtilizationRates(handle)
                info["utilization"] = util.gpu
                
                # Temperature
                temp = nvml.nvmlDeviceGetTemperature(handle, nvml.NVML_TEMPERATURE_GPU)
                info["temperature"] = temp
                
            except Exception as e:
                console.print(f"[yellow]Warning: Could not get detailed GPU metrics: {e}[/yellow]")
        
        return info
    
    def profile_memory_usage(self, device_id: int = 0) -> Dict[str, float]:
        """Profile GPU memory usage"""
        if not self.cuda_available:
            return {"memory_allocated": 0.0, "memory_reserved": 0.0, "memory_free": 0.0}
        
        allocated = torch.cuda.memory_allocated(device_id) / (1024**3)
        reserved = torch.cuda.memory_reserved(device_id) / (1024**3)
        total = torch.cuda.get_device_properties(device_id).total_memory / (1024**3)
        free = total - allocated
        
        return {
            "memory_allocated": allocated,
            "memory_reserved": reserved,
            "memory_free": free,
            "memory_total": total,
            "memory_utilization": (allocated / total) * 100 if total > 0 else 0.0,
        }

class MemoryProfiler:
    """System memory profiling utilities"""
    
    def __init__(self):
        self.process = psutil.Process()
    
    def get_memory_info(self) -> Dict[str, float]:
        """Get comprehensive memory information"""
        # System memory
        system_memory = psutil.virtual_memory()
        
        # Process memory
        process_memory = self.process.memory_info()
        
        return {
            "system_total_gb": system_memory.total / (1024**3),
            "system_available_gb": system_memory.available / (1024**3),
            "system_used_gb": system_memory.used / (1024**3),
            "system_percent": system_memory.percent,
            "process_rss_mb": process_memory.rss / (1024**2),
            "process_vms_mb": process_memory.vms / (1024**2),
        }
    
    def get_cpu_info(self) -> Dict[str, float]:
        """Get CPU utilization information"""
        return {
            "cpu_percent": psutil.cpu_percent(interval=0.1),
            "cpu_count": psutil.cpu_count(),
            "cpu_freq": psutil.cpu_freq().current if psutil.cpu_freq() else 0.0,
        }

class EfficiencyProfiler:
    """Main efficiency profiler combining GPU, CPU, and memory monitoring"""
    
    def __init__(self, device: Optional[torch.device] = None):
        self.device = device or torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.gpu_profiler = GPUProfiler()
        self.memory_profiler = MemoryProfiler()
        
        self.metrics_history: List[ProfileMetrics] = []
        self.monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        
    def start_monitoring(self, interval: float = 1.0):
        """Start continuous monitoring"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop, 
            args=(interval,), 
            daemon=True
        )
        self.monitor_thread.start()
        console.print("[green]Started efficiency monitoring[/green]")
    
    def stop_monitoring(self):
        """Stop continuous monitoring"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2.0)
        console.print("[yellow]Stopped efficiency monitoring[/yellow]")
    
    def _monitor_loop(self, interval: float):
        """Monitoring loop"""
        while self.monitoring:
            metrics = self.capture_metrics()
            self.metrics_history.append(metrics)
            time.sleep(interval)
    
    def capture_metrics(self) -> ProfileMetrics:
        """Capture current system metrics"""
        timestamp = time.time()
        
        # CPU and memory
        memory_info = self.memory_profiler.get_memory_info()
        cpu_info = self.memory_profiler.get_cpu_info()
        
        # GPU metrics
        gpu_memory = 0.0
        gpu_utilization = 0.0
        gpu_temperature = 0.0
        
        if self.device.type == "cuda":
            gpu_info = self.gpu_profiler.get_gpu_info(self.device.index or 0)
            gpu_memory = gpu_info.get("memory_allocated", 0.0) * 1024  # Convert to MB
            gpu_utilization = gpu_info.get("utilization", 0.0)
            gpu_temperature = gpu_info.get("temperature", 0.0)
        
        return ProfileMetrics(
            timestamp=timestamp,
            cpu_percent=cpu_info["cpu_percent"],
            memory_mb=memory_info["process_rss_mb"],
            gpu_memory_mb=gpu_memory,
            gpu_utilization=gpu_utilization,
            gpu_temperature=gpu_temperature,
        )
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """Get summary statistics from monitoring history"""
        if not self.metrics_history:
            return {}
        
        metrics_dict = {
            "cpu_percent": [m.cpu_percent for m in self.metrics_history],
            "memory_mb": [m.memory_mb for m in self.metrics_history],
            "gpu_memory_mb": [m.gpu_memory_mb for m in self.metrics_history],
            "gpu_utilization": [m.gpu_utilization for m in self.metrics_history],
            "gpu_temperature": [m.gpu_temperature for m in self.metrics_history],
        }
        
        summary = {}
        for key, values in metrics_dict.items():
            if values and any(v > 0 for v in values):
                summary[key] = {
                    "mean": np.mean(values),
                    "std": np.std(values),
                    "min": np.min(values),
                    "max": np.max(values),
                    "median": np.median(values),
                }
        
        return summary
    
    def save_metrics(self, filepath: Union[str, Path]):
        """Save metrics history to file"""
        filepath = Path(filepath)
        
        data = {
            "device": str(self.device),
            "metrics": [m.to_dict() for m in self.metrics_history],
            "summary": self.get_summary_stats(),
        }
        
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)
        
        console.print(f"[green]Saved metrics to {filepath}[/green]")
    
    def print_summary(self):
        """Print summary table"""
        summary = self.get_summary_stats()
        if not summary:
            console.print("[yellow]No metrics available[/yellow]")
            return
        
        table = Table(title="Efficiency Profiling Summary")
        table.add_column("Metric", style="cyan")
        table.add_column("Mean", style="green")
        table.add_column("Std", style="yellow")
        table.add_column("Min", style="blue")
        table.add_column("Max", style="red")
        
        for metric, stats in summary.items():
            table.add_row(
                metric.replace("_", " ").title(),
                f"{stats['mean']:.2f}",
                f"{stats['std']:.2f}",
                f"{stats['min']:.2f}",
                f"{stats['max']:.2f}",
            )
        
        console.print(table)

@contextmanager
def profile_training_step(profiler: EfficiencyProfiler, step_name: str = "training_step"):
    """Context manager for profiling training steps"""
    start_time = time.time()
    start_metrics = profiler.capture_metrics()
    
    try:
        yield
    finally:
        end_time = time.time()
        end_metrics = profiler.capture_metrics()
        
        execution_time = end_time - start_time
        
        console.print(
            f"[blue]{step_name}[/blue] completed in {execution_time:.3f}s | "
            f"Memory: {end_metrics.memory_mb:.1f}MB | "
            f"GPU Memory: {end_metrics.gpu_memory_mb:.1f}MB"
        )

@contextmanager  
def profile_model_inference(model: torch.nn.Module, device: torch.device, 
                          input_shape: tuple, batch_sizes: List[int] = None):
    """Profile model inference performance"""
    if batch_sizes is None:
        batch_sizes = [1, 8, 16, 32]
    
    profiler = EfficiencyProfiler(device)
    results = {}
    
    model.eval()
    with torch.no_grad():
        for batch_size in batch_sizes:
            # Create dummy input
            dummy_input = torch.randn(batch_size, *input_shape, device=device)
            
            # Warmup
            for _ in range(5):
                _ = model(dummy_input)
            
            # Profile
            torch.cuda.synchronize() if device.type == "cuda" else None
            start_time = time.time()
            start_metrics = profiler.capture_metrics()
            
            for _ in range(10):
                _ = model(dummy_input)
            
            torch.cuda.synchronize() if device.type == "cuda" else None
            end_time = time.time()
            end_metrics = profiler.capture_metrics()
            
            avg_time = (end_time - start_time) / 10
            throughput = batch_size / avg_time
            
            results[batch_size] = {
                "avg_inference_time": avg_time,
                "throughput": throughput,
                "memory_usage": end_metrics.memory_mb - start_metrics.memory_mb,
                "gpu_memory_usage": end_metrics.gpu_memory_mb - start_metrics.gpu_memory_mb,
            }
    
    # Print results
    table = Table(title="Model Inference Profiling")
    table.add_column("Batch Size", style="cyan")
    table.add_column("Avg Time (s)", style="green")
    table.add_column("Throughput (samples/s)", style="yellow")
    table.add_column("Memory (MB)", style="blue")
    table.add_column("GPU Memory (MB)", style="red")
    
    for batch_size, metrics in results.items():
        table.add_row(
            str(batch_size),
            f"{metrics['avg_inference_time']:.4f}",
            f"{metrics['throughput']:.1f}",
            f"{metrics['memory_usage']:.1f}",
            f"{metrics['gpu_memory_usage']:.1f}",
        )
    
    console.print(table)
    yield results

class TrainingEfficiencyAnalyzer:
    """Analyze training efficiency across different configurations"""

    def __init__(self, device: torch.device):
        self.device = device
        self.profiler = EfficiencyProfiler(device)
        self.training_metrics: Dict[str, List[ProfileMetrics]] = {}

    def start_training_analysis(self, experiment_name: str):
        """Start analyzing training efficiency for an experiment"""
        self.current_experiment = experiment_name
        self.training_metrics[experiment_name] = []
        self.profiler.start_monitoring(interval=0.5)

    def stop_training_analysis(self):
        """Stop training analysis"""
        self.profiler.stop_monitoring()
        if hasattr(self, 'current_experiment'):
            self.training_metrics[self.current_experiment] = self.profiler.metrics_history.copy()
            self.profiler.metrics_history.clear()

    def analyze_training_efficiency(self, experiment_name: str) -> Dict[str, Any]:
        """Analyze training efficiency for a specific experiment"""
        if experiment_name not in self.training_metrics:
            return {}

        metrics = self.training_metrics[experiment_name]
        if not metrics:
            return {}

        # Calculate efficiency metrics
        total_time = metrics[-1].timestamp - metrics[0].timestamp if len(metrics) > 1 else 0
        avg_cpu = np.mean([m.cpu_percent for m in metrics])
        avg_memory = np.mean([m.memory_mb for m in metrics])
        avg_gpu_memory = np.mean([m.gpu_memory_mb for m in metrics])
        avg_gpu_util = np.mean([m.gpu_utilization for m in metrics])

        # Resource utilization efficiency
        cpu_efficiency = min(avg_cpu / 80.0, 1.0)  # Target 80% CPU usage
        memory_efficiency = avg_memory / (psutil.virtual_memory().total / (1024**2))

        gpu_efficiency = 0.0
        if self.device.type == "cuda" and avg_gpu_util > 0:
            gpu_efficiency = min(avg_gpu_util / 85.0, 1.0)  # Target 85% GPU usage

        return {
            "experiment_name": experiment_name,
            "total_training_time": total_time,
            "average_cpu_percent": avg_cpu,
            "average_memory_mb": avg_memory,
            "average_gpu_memory_mb": avg_gpu_memory,
            "average_gpu_utilization": avg_gpu_util,
            "cpu_efficiency_score": cpu_efficiency,
            "memory_efficiency_score": memory_efficiency,
            "gpu_efficiency_score": gpu_efficiency,
            "overall_efficiency_score": np.mean([cpu_efficiency, gpu_efficiency]) if gpu_efficiency > 0 else cpu_efficiency,
            "resource_bottleneck": self._identify_bottleneck(avg_cpu, avg_gpu_util, memory_efficiency),
        }

    def _identify_bottleneck(self, cpu_percent: float, gpu_util: float, memory_efficiency: float) -> str:
        """Identify the primary resource bottleneck"""
        if memory_efficiency > 0.9:
            return "memory"
        elif cpu_percent > 90:
            return "cpu"
        elif gpu_util > 0 and gpu_util < 50:
            return "gpu_underutilized"
        elif gpu_util > 95:
            return "gpu"
        else:
            return "balanced"

    def compare_experiments(self, experiment_names: List[str]) -> Dict[str, Any]:
        """Compare efficiency across multiple experiments"""
        comparison = {}

        for exp_name in experiment_names:
            if exp_name in self.training_metrics:
                comparison[exp_name] = self.analyze_training_efficiency(exp_name)

        if len(comparison) < 2:
            return comparison

        # Find best performing experiment
        best_exp = max(comparison.keys(),
                      key=lambda x: comparison[x].get("overall_efficiency_score", 0))

        comparison["best_experiment"] = best_exp
        comparison["efficiency_ranking"] = sorted(
            comparison.keys(),
            key=lambda x: comparison[x].get("overall_efficiency_score", 0) if x != "best_experiment" else float('inf'),
            reverse=True
        )

        return comparison

    def generate_efficiency_report(self, output_dir: Union[str, Path]):
        """Generate comprehensive efficiency report"""
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        # Analyze all experiments
        all_analyses = {}
        for exp_name in self.training_metrics.keys():
            all_analyses[exp_name] = self.analyze_training_efficiency(exp_name)

        # Save detailed report
        report_path = output_dir / "efficiency_report.json"
        with open(report_path, 'w') as f:
            json.dump(all_analyses, f, indent=2)

        # Generate summary table
        if all_analyses:
            table = Table(title="Training Efficiency Analysis")
            table.add_column("Experiment", style="cyan")
            table.add_column("Training Time (s)", style="green")
            table.add_column("CPU Efficiency", style="yellow")
            table.add_column("GPU Efficiency", style="blue")
            table.add_column("Overall Score", style="red")
            table.add_column("Bottleneck", style="magenta")

            for exp_name, analysis in all_analyses.items():
                table.add_row(
                    exp_name,
                    f"{analysis.get('total_training_time', 0):.1f}",
                    f"{analysis.get('cpu_efficiency_score', 0):.2f}",
                    f"{analysis.get('gpu_efficiency_score', 0):.2f}",
                    f"{analysis.get('overall_efficiency_score', 0):.2f}",
                    analysis.get('resource_bottleneck', 'unknown'),
                )

            console.print(table)

        console.print(f"[green]Efficiency report saved to {report_path}[/green]")
        return report_path
