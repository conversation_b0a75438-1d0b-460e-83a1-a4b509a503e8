"""
Safety Metrics Module for Safe RL Framework

Provides comprehensive safety analysis tools including violation rate tracking,
constraint satisfaction analysis, and safety budget monitoring for Safe RL experiments.
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, List, Tuple, Optional, Union
from dataclasses import dataclass, field
from pathlib import Path
import json
import time

import matplotlib.pyplot as plt
import seaborn as sns
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn

console = Console()

@dataclass
class SafetyMetrics:
    """Container for safety metrics"""
    violation_rate: float
    total_violations: int
    episodes_with_violations: int
    max_violations_per_episode: int
    constraint_satisfaction_rate: float
    safety_budget_utilization: float
    cumulative_cost: float
    average_cost_per_episode: float
    safety_efficiency_score: float
    
    def to_dict(self) -> Dict[str, float]:
        """Convert to dictionary"""
        return {
            "violation_rate": self.violation_rate,
            "total_violations": self.total_violations,
            "episodes_with_violations": self.episodes_with_violations,
            "max_violations_per_episode": self.max_violations_per_episode,
            "constraint_satisfaction_rate": self.constraint_satisfaction_rate,
            "safety_budget_utilization": self.safety_budget_utilization,
            "cumulative_cost": self.cumulative_cost,
            "average_cost_per_episode": self.average_cost_per_episode,
            "safety_efficiency_score": self.safety_efficiency_score,
        }

def compute_violation_rate(violations: np.ndarray, window_size: Optional[int] = None) -> Union[float, np.ndarray]:
    """Compute violation rate with optional rolling window"""
    
    if window_size is None:
        return np.mean(violations)
    else:
        if len(violations) < window_size:
            return np.mean(violations)
        
        # Rolling window violation rate
        rolling_rates = []
        for i in range(window_size - 1, len(violations)):
            window_violations = violations[i - window_size + 1:i + 1]
            rolling_rates.append(np.mean(window_violations))
        
        return np.array(rolling_rates)

def analyze_constraint_satisfaction(constraints: np.ndarray, 
                                  constraint_names: Optional[List[str]] = None) -> Dict[str, Any]:
    """Analyze constraint satisfaction patterns"""
    
    if constraints.ndim == 1:
        # Single constraint
        constraints = constraints.reshape(-1, 1)
        constraint_names = constraint_names or ["constraint_0"]
    else:
        # Multiple constraints
        if constraint_names is None:
            constraint_names = [f"constraint_{i}" for i in range(constraints.shape[1])]
    
    analysis = {
        "individual_constraints": {},
        "joint_satisfaction": {},
        "violation_patterns": {},
    }
    
    # Individual constraint analysis
    for i, name in enumerate(constraint_names):
        constraint_data = constraints[:, i]
        
        analysis["individual_constraints"][name] = {
            "satisfaction_rate": 1 - np.mean(constraint_data),
            "violation_rate": np.mean(constraint_data),
            "total_violations": np.sum(constraint_data),
            "episodes_with_violations": np.sum(constraint_data > 0),
            "max_violations": np.max(constraint_data),
            "violation_frequency": np.mean(constraint_data > 0),
        }
    
    # Joint satisfaction analysis
    any_violation = np.any(constraints > 0, axis=1)
    all_satisfied = np.all(constraints == 0, axis=1)
    
    analysis["joint_satisfaction"] = {
        "all_constraints_satisfied_rate": np.mean(all_satisfied),
        "any_constraint_violated_rate": np.mean(any_violation),
        "simultaneous_violations": np.mean(np.sum(constraints > 0, axis=1) > 1),
    }
    
    # Violation patterns
    if len(constraint_names) > 1:
        violation_combinations = {}
        for episode in range(len(constraints)):
            violated_constraints = tuple(i for i, val in enumerate(constraints[episode]) if val > 0)
            if violated_constraints:
                key = "_".join([constraint_names[i] for i in violated_constraints])
                violation_combinations[key] = violation_combinations.get(key, 0) + 1
        
        analysis["violation_patterns"] = violation_combinations
    
    return analysis

class ViolationRateTracker:
    """Track and analyze safety violation rates over time"""
    
    def __init__(self, window_size: int = 100):
        self.window_size = window_size
        self.violation_history: List[int] = []
        self.episode_history: List[int] = []
        self.timestamp_history: List[float] = []
        
    def record_episode(self, episode: int, violations: int):
        """Record violations for an episode"""
        self.episode_history.append(episode)
        self.violation_history.append(violations)
        self.timestamp_history.append(time.time())
    
    def get_current_rate(self) -> float:
        """Get current violation rate"""
        if not self.violation_history:
            return 0.0
        
        recent_violations = self.violation_history[-self.window_size:]
        return np.mean(recent_violations)
    
    def get_rolling_rate(self, window: Optional[int] = None) -> np.ndarray:
        """Get rolling violation rate"""
        if not self.violation_history:
            return np.array([])
        
        window = window or self.window_size
        violations = np.array(self.violation_history)
        
        return compute_violation_rate(violations, window)
    
    def get_trend_analysis(self) -> Dict[str, Any]:
        """Analyze violation rate trends"""
        if len(self.violation_history) < 10:
            return {"trend": "insufficient_data"}
        
        violations = np.array(self.violation_history)
        episodes = np.array(self.episode_history)
        
        # Linear trend
        slope, intercept = np.polyfit(episodes, violations, 1)
        
        # Recent vs early comparison
        split_point = len(violations) // 2
        early_rate = np.mean(violations[:split_point])
        recent_rate = np.mean(violations[split_point:])
        
        # Trend classification
        if abs(slope) < 0.001:
            trend = "stable"
        elif slope < 0:
            trend = "improving"
        else:
            trend = "worsening"
        
        return {
            "trend": trend,
            "slope": slope,
            "early_rate": early_rate,
            "recent_rate": recent_rate,
            "improvement": early_rate - recent_rate,
            "relative_improvement": (early_rate - recent_rate) / early_rate if early_rate > 0 else 0,
        }

class ConstraintSatisfactionAnalyzer:
    """Analyze constraint satisfaction patterns and performance"""
    
    def __init__(self, constraint_names: Optional[List[str]] = None):
        self.constraint_names = constraint_names or []
        self.constraint_history: List[np.ndarray] = []
        self.episode_history: List[int] = []
        
    def record_episode(self, episode: int, constraint_violations: np.ndarray):
        """Record constraint violations for an episode"""
        self.episode_history.append(episode)
        self.constraint_history.append(constraint_violations.copy())
        
        # Update constraint names if needed
        if not self.constraint_names and len(constraint_violations) > 0:
            self.constraint_names = [f"constraint_{i}" for i in range(len(constraint_violations))]
    
    def get_satisfaction_rates(self) -> Dict[str, float]:
        """Get satisfaction rates for each constraint"""
        if not self.constraint_history:
            return {}
        
        all_constraints = np.array(self.constraint_history)
        satisfaction_rates = {}
        
        for i, name in enumerate(self.constraint_names):
            if i < all_constraints.shape[1]:
                constraint_data = all_constraints[:, i]
                satisfaction_rates[name] = 1 - np.mean(constraint_data > 0)
        
        return satisfaction_rates
    
    def get_violation_correlations(self) -> np.ndarray:
        """Get correlation matrix of constraint violations"""
        if not self.constraint_history or len(self.constraint_names) < 2:
            return np.array([])
        
        all_constraints = np.array(self.constraint_history)
        return np.corrcoef(all_constraints.T)
    
    def analyze_constraint_interactions(self) -> Dict[str, Any]:
        """Analyze interactions between constraints"""
        if not self.constraint_history or len(self.constraint_names) < 2:
            return {}
        
        all_constraints = np.array(self.constraint_history)
        n_constraints = len(self.constraint_names)
        
        interactions = {
            "pairwise_violations": {},
            "conditional_probabilities": {},
            "independence_tests": {},
        }
        
        # Pairwise violation analysis
        for i in range(n_constraints):
            for j in range(i + 1, n_constraints):
                name_i, name_j = self.constraint_names[i], self.constraint_names[j]
                
                violations_i = all_constraints[:, i] > 0
                violations_j = all_constraints[:, j] > 0
                
                # Joint violation probability
                joint_violations = np.sum(violations_i & violations_j)
                total_episodes = len(violations_i)
                
                interactions["pairwise_violations"][f"{name_i}_{name_j}"] = {
                    "joint_violation_rate": joint_violations / total_episodes,
                    "joint_violation_count": joint_violations,
                }
                
                # Conditional probabilities
                if np.sum(violations_i) > 0:
                    p_j_given_i = np.sum(violations_i & violations_j) / np.sum(violations_i)
                else:
                    p_j_given_i = 0.0
                
                if np.sum(violations_j) > 0:
                    p_i_given_j = np.sum(violations_i & violations_j) / np.sum(violations_j)
                else:
                    p_i_given_j = 0.0
                
                interactions["conditional_probabilities"][f"{name_i}_{name_j}"] = {
                    "p_j_given_i": p_j_given_i,
                    "p_i_given_j": p_i_given_j,
                }
        
        return interactions

class SafetyBudgetMonitor:
    """Monitor safety budget utilization and compliance"""
    
    def __init__(self, budget_limit: float = 0.1, budget_window: int = 1000):
        self.budget_limit = budget_limit
        self.budget_window = budget_window
        self.cost_history: List[float] = []
        self.episode_history: List[int] = []
        self.cumulative_cost = 0.0
        
    def record_episode_cost(self, episode: int, cost: float):
        """Record cost for an episode"""
        self.episode_history.append(episode)
        self.cost_history.append(cost)
        self.cumulative_cost += cost
    
    def get_current_utilization(self) -> float:
        """Get current budget utilization rate"""
        if not self.episode_history:
            return 0.0
        
        episodes_elapsed = len(self.episode_history)
        average_cost = self.cumulative_cost / episodes_elapsed
        
        return average_cost / self.budget_limit
    
    def get_budget_compliance(self) -> Dict[str, Any]:
        """Analyze budget compliance"""
        if not self.cost_history:
            return {"compliant": True, "utilization": 0.0}
        
        episodes_elapsed = len(self.cost_history)
        average_cost = self.cumulative_cost / episodes_elapsed
        current_utilization = average_cost / self.budget_limit
        
        # Rolling window compliance
        window_size = min(self.budget_window, episodes_elapsed)
        recent_costs = self.cost_history[-window_size:]
        recent_average = np.mean(recent_costs)
        recent_utilization = recent_average / self.budget_limit
        
        # Compliance status
        compliant = current_utilization <= 1.0
        recent_compliant = recent_utilization <= 1.0
        
        return {
            "compliant": compliant,
            "recent_compliant": recent_compliant,
            "utilization": current_utilization,
            "recent_utilization": recent_utilization,
            "budget_limit": self.budget_limit,
            "cumulative_cost": self.cumulative_cost,
            "average_cost_per_episode": average_cost,
            "episodes_over_budget": np.sum(np.array(self.cost_history) > self.budget_limit),
        }
    
    def predict_budget_exhaustion(self) -> Optional[int]:
        """Predict when budget might be exhausted"""
        if len(self.cost_history) < 10:
            return None
        
        recent_costs = self.cost_history[-50:]  # Use recent trend
        trend_slope = np.polyfit(range(len(recent_costs)), recent_costs, 1)[0]
        
        if trend_slope <= 0:
            return None  # Budget usage is stable or decreasing
        
        current_rate = np.mean(recent_costs)
        remaining_budget = self.budget_limit - current_rate
        
        if remaining_budget <= 0:
            return 0  # Already over budget
        
        episodes_to_exhaustion = remaining_budget / trend_slope
        return int(episodes_to_exhaustion) if episodes_to_exhaustion > 0 else None

class SafetyMetricsCalculator:
    """Main class for calculating comprehensive safety metrics"""

    def __init__(self, budget_limit: float = 0.1, constraint_names: Optional[List[str]] = None):
        self.budget_limit = budget_limit
        self.constraint_names = constraint_names or []

        # Initialize trackers
        self.violation_tracker = ViolationRateTracker()
        self.constraint_analyzer = ConstraintSatisfactionAnalyzer(constraint_names)
        self.budget_monitor = SafetyBudgetMonitor(budget_limit)

        # Data storage
        self.episode_data: List[Dict[str, Any]] = []

    def record_episode(self, episode: int, violations: int, constraints: np.ndarray,
                      cost: float, additional_info: Optional[Dict[str, Any]] = None):
        """Record safety data for an episode"""

        # Record in individual trackers
        self.violation_tracker.record_episode(episode, violations)
        self.constraint_analyzer.record_episode(episode, constraints)
        self.budget_monitor.record_episode_cost(episode, cost)

        # Store episode data
        episode_info = {
            "episode": episode,
            "violations": violations,
            "constraints": constraints.tolist() if isinstance(constraints, np.ndarray) else constraints,
            "cost": cost,
            "timestamp": time.time(),
        }

        if additional_info:
            episode_info.update(additional_info)

        self.episode_data.append(episode_info)

    def calculate_comprehensive_metrics(self) -> SafetyMetrics:
        """Calculate comprehensive safety metrics"""

        if not self.episode_data:
            return SafetyMetrics(
                violation_rate=0.0,
                total_violations=0,
                episodes_with_violations=0,
                max_violations_per_episode=0,
                constraint_satisfaction_rate=1.0,
                safety_budget_utilization=0.0,
                cumulative_cost=0.0,
                average_cost_per_episode=0.0,
                safety_efficiency_score=1.0,
            )

        # Extract data
        violations = np.array([ep["violations"] for ep in self.episode_data])

        # Basic violation metrics
        violation_rate = np.mean(violations)
        total_violations = np.sum(violations)
        episodes_with_violations = np.sum(violations > 0)
        max_violations_per_episode = np.max(violations)

        # Constraint satisfaction
        satisfaction_rates = self.constraint_analyzer.get_satisfaction_rates()
        overall_satisfaction_rate = np.mean(list(satisfaction_rates.values())) if satisfaction_rates else 1.0

        # Budget metrics
        budget_compliance = self.budget_monitor.get_budget_compliance()
        budget_utilization = budget_compliance["utilization"]
        cumulative_cost = budget_compliance["cumulative_cost"]
        average_cost_per_episode = budget_compliance["average_cost_per_episode"]

        # Safety efficiency score (combines low violations with high constraint satisfaction)
        violation_penalty = min(violation_rate / 0.1, 1.0)  # Normalize to 0.1 violations per episode
        budget_penalty = min(budget_utilization, 1.0)
        safety_efficiency_score = (overall_satisfaction_rate * (1 - violation_penalty) * (1 - budget_penalty))

        return SafetyMetrics(
            violation_rate=violation_rate,
            total_violations=int(total_violations),
            episodes_with_violations=int(episodes_with_violations),
            max_violations_per_episode=int(max_violations_per_episode),
            constraint_satisfaction_rate=overall_satisfaction_rate,
            safety_budget_utilization=budget_utilization,
            cumulative_cost=cumulative_cost,
            average_cost_per_episode=average_cost_per_episode,
            safety_efficiency_score=safety_efficiency_score,
        )

    def generate_safety_report(self) -> Dict[str, Any]:
        """Generate comprehensive safety analysis report"""

        metrics = self.calculate_comprehensive_metrics()

        report = {
            "summary_metrics": metrics.to_dict(),
            "violation_analysis": self.violation_tracker.get_trend_analysis(),
            "constraint_analysis": self.constraint_analyzer.analyze_constraint_interactions(),
            "budget_analysis": self.budget_monitor.get_budget_compliance(),
            "recommendations": self._generate_recommendations(metrics),
        }

        return report

    def _generate_recommendations(self, metrics: SafetyMetrics) -> List[str]:
        """Generate safety improvement recommendations"""

        recommendations = []

        # Violation rate recommendations
        if metrics.violation_rate > 0.1:
            recommendations.append("High violation rate detected. Consider increasing safety constraints or reducing exploration.")
        elif metrics.violation_rate > 0.05:
            recommendations.append("Moderate violation rate. Monitor closely and consider constraint tuning.")

        # Constraint satisfaction recommendations
        if metrics.constraint_satisfaction_rate < 0.8:
            recommendations.append("Low constraint satisfaction. Review constraint definitions and agent training.")
        elif metrics.constraint_satisfaction_rate < 0.9:
            recommendations.append("Room for improvement in constraint satisfaction. Consider constraint-aware training.")

        # Budget recommendations
        if metrics.safety_budget_utilization > 1.0:
            recommendations.append("Safety budget exceeded. Implement stricter safety measures or increase budget.")
        elif metrics.safety_budget_utilization > 0.8:
            recommendations.append("Approaching safety budget limit. Monitor closely and prepare contingency measures.")

        # Efficiency recommendations
        if metrics.safety_efficiency_score < 0.5:
            recommendations.append("Low safety efficiency. Balance safety constraints with performance objectives.")
        elif metrics.safety_efficiency_score < 0.7:
            recommendations.append("Moderate safety efficiency. Fine-tune safety-performance trade-offs.")

        if not recommendations:
            recommendations.append("Safety metrics are within acceptable ranges. Continue monitoring.")

        return recommendations

    def print_safety_summary(self):
        """Print formatted safety summary"""

        metrics = self.calculate_comprehensive_metrics()

        # Create summary table
        table = Table(title="Safety Metrics Summary")
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="green")
        table.add_column("Status", style="yellow")

        # Violation metrics
        violation_status = "🟢 Good" if metrics.violation_rate < 0.05 else "🟡 Moderate" if metrics.violation_rate < 0.1 else "🔴 High"
        table.add_row("Violation Rate", f"{metrics.violation_rate:.4f}", violation_status)
        table.add_row("Total Violations", str(metrics.total_violations), "")

        # Constraint satisfaction
        satisfaction_status = "🟢 Excellent" if metrics.constraint_satisfaction_rate > 0.9 else "🟡 Good" if metrics.constraint_satisfaction_rate > 0.8 else "🔴 Poor"
        table.add_row("Constraint Satisfaction", f"{metrics.constraint_satisfaction_rate:.3f}", satisfaction_status)

        # Budget utilization
        budget_status = "🟢 Good" if metrics.safety_budget_utilization < 0.8 else "🟡 High" if metrics.safety_budget_utilization < 1.0 else "🔴 Exceeded"
        table.add_row("Budget Utilization", f"{metrics.safety_budget_utilization:.3f}", budget_status)

        # Overall efficiency
        efficiency_status = "🟢 Excellent" if metrics.safety_efficiency_score > 0.8 else "🟡 Good" if metrics.safety_efficiency_score > 0.6 else "🔴 Poor"
        table.add_row("Safety Efficiency Score", f"{metrics.safety_efficiency_score:.3f}", efficiency_status)

        console.print(table)

        # Print recommendations
        recommendations = self._generate_recommendations(metrics)
        if recommendations:
            console.print("\n[bold blue]Recommendations:[/bold blue]")
            for i, rec in enumerate(recommendations, 1):
                console.print(f"{i}. {rec}")

    def save_safety_data(self, filepath: Union[str, Path]):
        """Save safety data and analysis to file"""
        filepath = Path(filepath)

        report = self.generate_safety_report()
        report["episode_data"] = self.episode_data
        report["configuration"] = {
            "budget_limit": self.budget_limit,
            "constraint_names": self.constraint_names,
        }

        with open(filepath, 'w') as f:
            json.dump(report, f, indent=2, default=str)

        console.print(f"[green]Safety data saved to {filepath}[/green]")

    def plot_safety_trends(self, save_path: Optional[Union[str, Path]] = None):
        """Plot safety trends over time"""

        if not self.episode_data:
            console.print("[yellow]No data available for plotting[/yellow]")
            return

        episodes = [ep["episode"] for ep in self.episode_data]
        violations = [ep["violations"] for ep in self.episode_data]
        costs = [ep["cost"] for ep in self.episode_data]

        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Safety Metrics Trends', fontsize=16, fontweight='bold')

        # Violation rate over time
        rolling_violations = self.violation_tracker.get_rolling_rate()
        if len(rolling_violations) > 0:
            axes[0, 0].plot(episodes[-len(rolling_violations):], rolling_violations)
            axes[0, 0].set_title('Rolling Violation Rate')
            axes[0, 0].set_xlabel('Episode')
            axes[0, 0].set_ylabel('Violations per Episode')
            axes[0, 0].grid(True, alpha=0.3)

        # Cumulative cost
        cumulative_costs = np.cumsum(costs)
        axes[0, 1].plot(episodes, cumulative_costs)
        axes[0, 1].axhline(y=self.budget_limit * len(episodes), color='r', linestyle='--', label='Budget Limit')
        axes[0, 1].set_title('Cumulative Safety Cost')
        axes[0, 1].set_xlabel('Episode')
        axes[0, 1].set_ylabel('Cumulative Cost')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

        # Violation distribution
        axes[1, 0].hist(violations, bins=max(10, int(np.max(violations))), alpha=0.7, edgecolor='black')
        axes[1, 0].set_title('Violation Distribution')
        axes[1, 0].set_xlabel('Violations per Episode')
        axes[1, 0].set_ylabel('Frequency')
        axes[1, 0].grid(True, alpha=0.3)

        # Cost per episode
        axes[1, 1].plot(episodes, costs, alpha=0.6)
        axes[1, 1].axhline(y=self.budget_limit, color='r', linestyle='--', label='Budget Limit')
        axes[1, 1].set_title('Cost per Episode')
        axes[1, 1].set_xlabel('Episode')
        axes[1, 1].set_ylabel('Cost')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            console.print(f"[green]Safety trends plot saved to {save_path}[/green]")

        plt.show()
