#!/usr/bin/env python3
"""
Example Usage of Safe RL Metrics Package

This script demonstrates how to use the comprehensive metrics package
for analyzing efficiency, statistical significance, and safety in Safe RL experiments.
"""

import sys
import numpy as np
import torch
from pathlib import Path
import time

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn

# Import metrics modules
from metrics import (
    EfficiencyProfiler, StatisticalAnalyzer, SafetyMetricsCalculator,
    PerformanceComparator, TrainingEfficiencyAnalyzer
)

console = Console()

def simulate_safe_rl_training():
    """Simulate a Safe RL training session with comprehensive metrics"""
    
    console.print(Panel.fit("🚀 Safe RL Training with Comprehensive Metrics", style="bold blue"))
    
    # Setup
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    console.print(f"[green]Using device: {device}[/green]")
    
    # Initialize metrics components
    efficiency_profiler = EfficiencyProfiler(device)
    training_analyzer = TrainingEfficiencyAnalyzer(device)
    safety_calculator = SafetyMetricsCalculator(
        budget_limit=0.1, 
        constraint_names=["Safety", "Performance", "Efficiency"]
    )
    performance_comparator = PerformanceComparator()
    
    # Simulate training for two different agents
    agents = ["SafePPO", "StandardPPO"]
    n_episodes = 100
    
    console.print(f"\n[cyan]Simulating training for {n_episodes} episodes...[/cyan]")
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
        TimeElapsedColumn(),
        console=console
    ) as progress:
        
        for agent_name in agents:
            task = progress.add_task(f"Training {agent_name}", total=n_episodes)
            
            # Start efficiency monitoring
            training_analyzer.start_training_analysis(f"{agent_name}_experiment")
            
            # Simulate episodes
            episode_rewards = []
            
            for episode in range(n_episodes):
                # Simulate episode with different characteristics for each agent
                if agent_name == "SafePPO":
                    # Safe agent: lower rewards but fewer violations
                    reward = np.random.normal(12, 2)
                    violations = np.random.poisson(0.2)  # Lower violation rate
                    constraints = np.random.binomial(1, 0.05, 3)  # Better constraint satisfaction
                    cost = np.random.exponential(0.03)  # Lower safety cost
                else:
                    # Standard agent: higher rewards but more violations
                    reward = np.random.normal(15, 2.5)
                    violations = np.random.poisson(0.8)  # Higher violation rate
                    constraints = np.random.binomial(1, 0.15, 3)  # Worse constraint satisfaction
                    cost = np.random.exponential(0.07)  # Higher safety cost
                
                episode_rewards.append(reward)
                
                # Record safety metrics
                safety_calculator.record_episode(episode, violations, constraints, cost)
                
                # Simulate some computation time
                time.sleep(0.01)
                
                progress.advance(task)
            
            # Stop efficiency monitoring
            training_analyzer.stop_training_analysis()
            
            # Store performance data
            performance_comparator.add_performance_data(agent_name, "reward", np.array(episode_rewards))
    
    return efficiency_profiler, training_analyzer, safety_calculator, performance_comparator

def analyze_results(efficiency_profiler, training_analyzer, safety_calculator, performance_comparator):
    """Analyze and display comprehensive results"""
    
    console.print(Panel.fit("📊 Comprehensive Analysis Results", style="bold green"))
    
    # 1. Efficiency Analysis
    console.print("\n[bold blue]1. Training Efficiency Analysis[/bold blue]")
    efficiency_profiler.print_summary()
    
    # Compare training efficiency between experiments
    comparison = training_analyzer.compare_experiments(["SafePPO_experiment", "StandardPPO_experiment"])
    if comparison:
        console.print("\n[yellow]Training Efficiency Comparison:[/yellow]")
        for exp_name, metrics in comparison.items():
            if exp_name not in ["best_experiment", "efficiency_ranking"]:
                console.print(f"  {exp_name}: Efficiency Score = {metrics.get('overall_efficiency_score', 0):.3f}")
    
    # 2. Statistical Analysis
    console.print("\n[bold blue]2. Statistical Performance Comparison[/bold blue]")
    
    # Compare agent performance
    result = performance_comparator.compare_agents("SafePPO", "StandardPPO", "reward")
    console.print(f"Performance comparison: {result.test_name}")
    console.print(f"  p-value: {result.p_value:.4f}")
    console.print(f"  Effect size: {result.effect_size:.3f} ({result.effect_size_interpretation})")
    console.print(f"  Statistically significant: {result.significant}")
    
    # Rank agents
    rankings = performance_comparator.rank_agents("reward", higher_is_better=True)
    console.print("\n[yellow]Agent Performance Rankings:[/yellow]")
    for i, (agent, mean_reward, std_reward) in enumerate(rankings, 1):
        console.print(f"  {i}. {agent}: {mean_reward:.2f} ± {std_reward:.2f}")
    
    # 3. Safety Analysis
    console.print("\n[bold blue]3. Safety Metrics Analysis[/bold blue]")
    safety_calculator.print_safety_summary()
    
    # Generate safety report
    safety_report = safety_calculator.generate_safety_report()
    
    console.print("\n[yellow]Safety Recommendations:[/yellow]")
    for i, recommendation in enumerate(safety_report["recommendations"], 1):
        console.print(f"  {i}. {recommendation}")
    
    return safety_report

def save_comprehensive_report(efficiency_profiler, training_analyzer, safety_calculator, safety_report):
    """Save comprehensive analysis report"""
    
    console.print(Panel.fit("💾 Saving Comprehensive Report", style="bold magenta"))
    
    # Create results directory
    results_dir = Path("metrics_analysis_results")
    results_dir.mkdir(exist_ok=True)
    
    # Save efficiency metrics
    efficiency_profiler.save_metrics(results_dir / "efficiency_metrics.json")
    
    # Save training efficiency report
    training_analyzer.generate_efficiency_report(results_dir)
    
    # Save safety analysis
    safety_calculator.save_safety_data(results_dir / "safety_analysis.json")
    
    # Generate safety trends plot
    try:
        safety_calculator.plot_safety_trends(results_dir / "safety_trends.png")
    except Exception as e:
        console.print(f"[yellow]Could not generate safety plot: {e}[/yellow]")
    
    console.print(f"[green]✅ All reports saved to {results_dir}/[/green]")
    
    return results_dir

def demonstrate_advanced_features():
    """Demonstrate advanced metrics features"""
    
    console.print(Panel.fit("🔬 Advanced Metrics Features", style="bold cyan"))
    
    # 1. Statistical Analysis with Multiple Comparisons
    console.print("\n[bold blue]1. Multiple Comparisons with Correction[/bold blue]")
    
    analyzer = StatisticalAnalyzer(alpha=0.05)
    
    # Simulate multiple agent comparisons
    agents_data = {
        "PPO": np.random.normal(10, 2, 50),
        "SafePPO": np.random.normal(12, 1.5, 50),
        "MetaPPO": np.random.normal(11, 2.2, 50),
        "TransformerPPO": np.random.normal(13, 1.8, 50),
    }
    
    # Perform pairwise comparisons
    agent_names = list(agents_data.keys())
    for i in range(len(agent_names)):
        for j in range(i + 1, len(agent_names)):
            agent1, agent2 = agent_names[i], agent_names[j]
            analyzer.compare_groups(
                agents_data[agent1], agents_data[agent2], 
                agent1, agent2, paired=False
            )
    
    # Apply multiple comparisons correction
    corrected_results = analyzer.multiple_comparisons_correction(method="bonferroni")
    
    console.print("Pairwise comparisons (Bonferroni corrected):")
    for comparison, result in corrected_results.items():
        console.print(f"  {comparison}: p={result.p_value:.4f}, significant={result.significant}")
    
    # 2. Advanced Safety Analysis
    console.print("\n[bold blue]2. Advanced Safety Constraint Analysis[/bold blue]")
    
    # Simulate complex constraint interactions
    safety_calc = SafetyMetricsCalculator(
        budget_limit=0.08,
        constraint_names=["Collision_Avoidance", "Speed_Limit", "Energy_Efficiency", "Task_Completion"]
    )
    
    for episode in range(80):
        # Simulate correlated constraint violations
        base_violation_prob = 0.1 + 0.05 * np.sin(episode / 10)  # Varying difficulty
        
        violations = np.random.poisson(base_violation_prob * 5)
        
        # Correlated constraints (if one fails, others more likely to fail)
        constraint_base = np.random.binomial(1, base_violation_prob, 4)
        if np.any(constraint_base):
            # Increase probability of other violations
            constraint_violations = np.random.binomial(1, min(0.3, base_violation_prob * 2), 4)
        else:
            constraint_violations = constraint_base
        
        cost = np.random.exponential(0.04) * (1 + violations * 0.1)
        
        safety_calc.record_episode(episode, violations, constraint_violations, cost)
    
    # Analyze constraint interactions
    report = safety_calc.generate_safety_report()
    
    console.print("Constraint interaction analysis:")
    if "constraint_analysis" in report:
        interactions = report["constraint_analysis"]
        if "pairwise_violations" in interactions:
            for pair, data in interactions["pairwise_violations"].items():
                console.print(f"  {pair}: Joint violation rate = {data['joint_violation_rate']:.3f}")
    
    console.print("\n[green]✅ Advanced features demonstration completed![/green]")

def main():
    """Main demonstration function"""
    
    console.print(Panel.fit("🎯 Safe RL Metrics Package - Complete Demonstration", style="bold magenta"))
    
    try:
        # 1. Simulate training with metrics
        efficiency_profiler, training_analyzer, safety_calculator, performance_comparator = simulate_safe_rl_training()
        
        # 2. Analyze results
        safety_report = analyze_results(efficiency_profiler, training_analyzer, safety_calculator, performance_comparator)
        
        # 3. Save comprehensive report
        results_dir = save_comprehensive_report(efficiency_profiler, training_analyzer, safety_calculator, safety_report)
        
        # 4. Demonstrate advanced features
        demonstrate_advanced_features()
        
        # Final summary
        console.print(Panel.fit("🎉 Demonstration Complete!", style="bold green"))
        console.print(f"[cyan]Results saved to: {results_dir}[/cyan]")
        console.print("[yellow]The metrics package provides comprehensive analysis for:[/yellow]")
        console.print("  • GPU/CPU/Memory efficiency profiling")
        console.print("  • Statistical significance testing with effect sizes")
        console.print("  • Safety violation tracking and constraint analysis")
        console.print("  • Performance comparisons across agents")
        console.print("  • Training efficiency optimization recommendations")
        
    except Exception as e:
        console.print(f"[bold red]Error during demonstration: {e}[/bold red]")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
