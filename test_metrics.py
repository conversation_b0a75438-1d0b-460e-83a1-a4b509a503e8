#!/usr/bin/env python3
"""
Test script for the metrics package to verify all components work correctly.
"""

import sys
import numpy as np
import torch
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from rich.console import Console
from rich.panel import Panel

console = Console()

def test_efficiency_analysis():
    """Test efficiency analysis components"""
    console.print(Panel.fit("🔧 Testing Efficiency Analysis", style="bold blue"))
    
    try:
        from metrics.efficiency_analysis import (
            EfficiencyProfiler, GPUProfiler, MemoryProfiler, 
            TrainingEfficiencyAnalyzer, profile_training_step
        )
        
        # Test device detection
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        console.print(f"[green]✓ Device detected: {device}[/green]")
        
        # Test GPU profiler
        gpu_profiler = GPUProfiler()
        gpu_info = gpu_profiler.get_gpu_info()
        console.print(f"[green]✓ GPU profiler initialized - CUDA available: {gpu_info.get('available', False)}[/green]")
        
        # Test memory profiler
        memory_profiler = MemoryProfiler()
        memory_info = memory_profiler.get_memory_info()
        console.print(f"[green]✓ Memory profiler - System memory: {memory_info['system_total_gb']:.1f}GB[/green]")
        
        # Test efficiency profiler
        profiler = EfficiencyProfiler(device)
        metrics = profiler.capture_metrics()
        console.print(f"[green]✓ Efficiency profiler - CPU: {metrics.cpu_percent:.1f}%, Memory: {metrics.memory_mb:.1f}MB[/green]")
        
        # Test training efficiency analyzer
        analyzer = TrainingEfficiencyAnalyzer(device)
        console.print("[green]✓ Training efficiency analyzer initialized[/green]")
        
        console.print("[bold green]✅ Efficiency Analysis: All tests passed![/bold green]\n")
        return True
        
    except Exception as e:
        console.print(f"[bold red]❌ Efficiency Analysis failed: {e}[/bold red]\n")
        return False

def test_statistical_tests():
    """Test statistical analysis components"""
    console.print(Panel.fit("📊 Testing Statistical Analysis", style="bold blue"))
    
    try:
        from metrics.statistical_tests import (
            StatisticalAnalyzer, PerformanceComparator, SafetyStatistics,
            compute_effect_size, paired_t_test, wilcoxon_test, bootstrap_confidence_interval
        )
        
        # Generate test data
        np.random.seed(42)
        group1 = np.random.normal(10, 2, 50)
        group2 = np.random.normal(12, 2, 50)
        
        # Test effect size calculation
        effect_size, interpretation = compute_effect_size(group1, group2)
        console.print(f"[green]✓ Effect size calculation - Cohen's d: {effect_size:.3f} ({interpretation})[/green]")
        
        # Test paired t-test
        before = np.random.normal(8, 1.5, 30)
        after = before + np.random.normal(2, 0.5, 30)
        result = paired_t_test(before, after)
        console.print(f"[green]✓ Paired t-test - p-value: {result.p_value:.4f}, significant: {result.significant}[/green]")
        
        # Test bootstrap confidence interval
        data = np.random.normal(10, 2, 100)
        ci = bootstrap_confidence_interval(data, np.mean)
        console.print(f"[green]✓ Bootstrap CI - Mean: {np.mean(data):.2f}, CI: [{ci[0]:.2f}, {ci[1]:.2f}][/green]")
        
        # Test statistical analyzer
        analyzer = StatisticalAnalyzer()
        result = analyzer.compare_groups(group1, group2, "Group A", "Group B")
        console.print(f"[green]✓ Statistical analyzer - {result.test_name}, p-value: {result.p_value:.4f}[/green]")
        
        # Test performance comparator
        comparator = PerformanceComparator()
        comparator.add_performance_data("Agent1", "reward", group1)
        comparator.add_performance_data("Agent2", "reward", group2)
        rankings = comparator.rank_agents("reward")
        console.print(f"[green]✓ Performance comparator - Best agent: {rankings[0][0]} (mean: {rankings[0][1]:.2f})[/green]")
        
        # Test safety statistics
        safety_stats = SafetyStatistics()
        violations = {"Agent1": np.random.poisson(0.5, 100), "Agent2": np.random.poisson(1.2, 100)}
        analysis = safety_stats.analyze_violation_rates(violations)
        console.print(f"[green]✓ Safety statistics - Analyzed {len(violations)} agents[/green]")
        
        console.print("[bold green]✅ Statistical Analysis: All tests passed![/bold green]\n")
        return True
        
    except Exception as e:
        console.print(f"[bold red]❌ Statistical Analysis failed: {e}[/bold red]\n")
        return False

def test_safety_metrics():
    """Test safety metrics components"""
    console.print(Panel.fit("🛡️ Testing Safety Metrics", style="bold blue"))
    
    try:
        from metrics.safety_metrics import (
            SafetyMetricsCalculator, ViolationRateTracker, ConstraintSatisfactionAnalyzer,
            SafetyBudgetMonitor, compute_violation_rate, analyze_constraint_satisfaction
        )
        
        # Test violation rate computation
        violations = np.random.poisson(0.8, 100)
        rate = compute_violation_rate(violations)
        console.print(f"[green]✓ Violation rate computation - Rate: {rate:.3f}[/green]")
        
        # Test constraint satisfaction analysis
        constraints = np.random.binomial(1, 0.1, (100, 3))  # 3 constraints, 10% violation rate
        analysis = analyze_constraint_satisfaction(constraints, ["Safety", "Performance", "Efficiency"])
        console.print(f"[green]✓ Constraint analysis - {len(analysis['individual_constraints'])} constraints analyzed[/green]")
        
        # Test violation rate tracker
        tracker = ViolationRateTracker()
        for i in range(50):
            tracker.record_episode(i, np.random.poisson(0.5))
        current_rate = tracker.get_current_rate()
        trend = tracker.get_trend_analysis()
        console.print(f"[green]✓ Violation tracker - Current rate: {current_rate:.3f}, Trend: {trend['trend']}[/green]")
        
        # Test constraint satisfaction analyzer
        constraint_analyzer = ConstraintSatisfactionAnalyzer(["Safety", "Efficiency"])
        for i in range(30):
            constraint_analyzer.record_episode(i, np.random.binomial(1, 0.15, 2))
        satisfaction_rates = constraint_analyzer.get_satisfaction_rates()
        console.print(f"[green]✓ Constraint analyzer - Safety satisfaction: {satisfaction_rates.get('Safety', 0):.3f}[/green]")
        
        # Test safety budget monitor
        budget_monitor = SafetyBudgetMonitor(budget_limit=0.1)
        for i in range(40):
            budget_monitor.record_episode_cost(i, np.random.exponential(0.05))
        compliance = budget_monitor.get_budget_compliance()
        console.print(f"[green]✓ Budget monitor - Utilization: {compliance['utilization']:.3f}, Compliant: {compliance['compliant']}[/green]")
        
        # Test comprehensive safety metrics calculator
        calculator = SafetyMetricsCalculator(budget_limit=0.1, constraint_names=["Safety", "Performance"])
        
        # Simulate some episodes
        for episode in range(50):
            violations = np.random.poisson(0.3)
            constraints = np.random.binomial(1, 0.1, 2)
            cost = np.random.exponential(0.04)
            calculator.record_episode(episode, violations, constraints, cost)
        
        metrics = calculator.calculate_comprehensive_metrics()
        console.print(f"[green]✓ Safety calculator - Violation rate: {metrics.violation_rate:.3f}, "
                     f"Efficiency score: {metrics.safety_efficiency_score:.3f}[/green]")
        
        console.print("[bold green]✅ Safety Metrics: All tests passed![/bold green]\n")
        return True
        
    except Exception as e:
        console.print(f"[bold red]❌ Safety Metrics failed: {e}[/bold red]\n")
        return False

def test_integration():
    """Test integration between modules"""
    console.print(Panel.fit("🔗 Testing Module Integration", style="bold blue"))
    
    try:
        # Test importing all modules together
        from metrics import (
            EfficiencyProfiler, StatisticalAnalyzer, SafetyMetricsCalculator,
            compute_effect_size, compute_violation_rate
        )
        
        console.print("[green]✓ All modules imported successfully[/green]")
        
        # Test a simple integration scenario
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Create instances
        profiler = EfficiencyProfiler(device)
        analyzer = StatisticalAnalyzer()
        safety_calc = SafetyMetricsCalculator()
        
        console.print("[green]✓ All main classes instantiated successfully[/green]")
        
        # Simulate a mini experiment
        agent1_rewards = np.random.normal(15, 3, 30)
        agent2_rewards = np.random.normal(12, 3, 30)
        
        # Statistical comparison
        result = analyzer.compare_groups(agent1_rewards, agent2_rewards, "PPO", "SafePPO")
        
        # Safety analysis
        for episode in range(30):
            violations = np.random.poisson(0.2)
            constraints = np.random.binomial(1, 0.05, 2)
            cost = np.random.exponential(0.03)
            safety_calc.record_episode(episode, violations, constraints, cost)
        
        safety_metrics = safety_calc.calculate_comprehensive_metrics()
        
        console.print(f"[green]✓ Integration test - Statistical significance: {result.significant}, "
                     f"Safety efficiency: {safety_metrics.safety_efficiency_score:.3f}[/green]")
        
        console.print("[bold green]✅ Module Integration: All tests passed![/bold green]\n")
        return True
        
    except Exception as e:
        console.print(f"[bold red]❌ Module Integration failed: {e}[/bold red]\n")
        return False

def main():
    """Run all tests"""
    console.print(Panel.fit("🧪 Safe RL Metrics Package Test Suite", style="bold magenta"))
    
    tests = [
        ("Efficiency Analysis", test_efficiency_analysis),
        ("Statistical Tests", test_statistical_tests),
        ("Safety Metrics", test_safety_metrics),
        ("Module Integration", test_integration),
    ]
    
    results = []
    for test_name, test_func in tests:
        console.print(f"\n[bold cyan]Running {test_name} tests...[/bold cyan]")
        success = test_func()
        results.append((test_name, success))
    
    # Summary
    console.print(Panel.fit("📋 Test Results Summary", style="bold yellow"))
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "[bold green]✅ PASSED[/bold green]" if success else "[bold red]❌ FAILED[/bold red]"
        console.print(f"{test_name}: {status}")
    
    console.print(f"\n[bold]Overall: {passed}/{total} tests passed[/bold]")
    
    if passed == total:
        console.print("[bold green]🎉 All metrics components are working correctly![/bold green]")
        console.print("\n[cyan]The metrics package is ready for use in your Safe RL experiments.[/cyan]")
    else:
        console.print("[bold red]⚠️ Some tests failed. Please check the error messages above.[/bold red]")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
