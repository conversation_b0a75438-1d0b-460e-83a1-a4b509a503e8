"""
Metrics Package for Safe RL Framework

This package provides comprehensive metrics and analysis tools for Safe RL experiments:
- Efficiency analysis: GPU/CPU/memory profiling
- Statistical tests: p-values, effect sizes, significance testing
- Safety metrics: violation rates, constraint satisfaction
"""

from .efficiency_analysis import (
    EfficiencyProfiler,
    GPUProfiler,
    MemoryProfiler,
    TrainingEfficiencyAnalyzer,
    profile_training_step,
    profile_model_inference,
)

from .statistical_tests import (
    StatisticalAnalyzer,
    PerformanceComparator,
    SafetyStatistics,
    compute_effect_size,
    paired_t_test,
    wilcoxon_test,
    bootstrap_confidence_interval,
)

from .safety_metrics import (
    SafetyMetricsCalculator,
    ConstraintSatisfactionAnalyzer,
    ViolationRateTracker,
    SafetyBudgetMonitor,
    compute_violation_rate,
    analyze_constraint_satisfaction,
)

__all__ = [
    # Efficiency Analysis
    "EfficiencyProfiler",
    "GPUProfiler",
    "MemoryProfiler",
    "TrainingEfficiencyAnalyzer",
    "profile_training_step",
    "profile_model_inference",

    # Statistical Tests
    "StatisticalAnalyzer",
    "PerformanceComparator",
    "SafetyStatistics",
    "compute_effect_size",
    "paired_t_test",
    "wilcoxon_test",
    "bootstrap_confidence_interval",

    # Safety Metrics
    "SafetyMetricsCalculator",
    "ConstraintSatisfactionAnalyzer",
    "ViolationRateTracker",
    "SafetyBudgetMonitor",
    "compute_violation_rate",
    "analyze_constraint_satisfaction",
]
